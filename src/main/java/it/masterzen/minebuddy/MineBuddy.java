package it.masterzen.minebuddy;

import com.sk89q.worldedit.Vector;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.association.RegionAssociable;
import com.sk89q.worldguard.protection.flags.DefaultFlag;
import com.sk89q.worldguard.protection.flags.StateFlag;
import it.masterzen.blockbreak.AlphaBlockBreak;
import it.masterzen.blockbreak.BlockBreakData;
import it.masterzen.prestigemine.Main;
import it.masterzen.prestigemine.MineManager;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Mine Buddy system - automatically breaks blocks for players based on their location
 * Runs every 5 seconds and checks all online players for eligibility
 */
public class MineBuddy {

    private final AlphaBlockBreak plugin;
    private final String prefix = "§e§lMINE BUDDY §8»§7 ";

    private BukkitTask scheduledTask;
    private boolean enabled = true;

    // Location types for Mine Buddy processing
    public enum LocationType {
        MINE,
        FARMER_WARP,
        DUNGEON,
        NONE
    }

    public MineBuddy(AlphaBlockBreak plugin) {
        this.plugin = plugin;
    }

    /**
     * Starts the Mine Buddy scheduled task
     */
    public void start() {
        if (scheduledTask != null) {
            scheduledTask.cancel();
        }

        // Run every 5 seconds (100 ticks)
        scheduledTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (enabled) {
                    processAllPlayers();
                }
            }
        }.runTaskTimer(plugin, 100L, 100L); // Start after 5 seconds, repeat every 5 seconds

        plugin.getLogger().info("Mine Buddy system started - checking players every 5 seconds");
    }

    /**
     * Stops the Mine Buddy scheduled task
     */
    public void stop() {
        if (scheduledTask != null) {
            scheduledTask.cancel();
            scheduledTask = null;
        }
        plugin.getLogger().info("Mine Buddy system stopped");
    }

    /**
     * Enables or disables the Mine Buddy system
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        plugin.getLogger().info("Mine Buddy system " + (enabled ? "enabled" : "disabled"));
    }

    public boolean isEnabled() {
        return enabled;
    }

    /**
     * Main processing method - checks all online players
     */
    private void processAllPlayers() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            try {
                processPlayer(player);
            } catch (Exception e) {
                plugin.getLogger().warning("Error processing Mine Buddy for player " + player.getName() + ": " + e.getMessage());
            }
        }
    }

    /**
     * Processes a single player for Mine Buddy functionality
     */
    public void processPlayer(Player player) {
        // Basic validation
        if (!isPlayerEligible(player)) {
            return;
        }

        // Determine player location type
        LocationType locationType = getPlayerLocationType(player);
        if (locationType == LocationType.NONE) {
            return;
        }

        if (locationType != LocationType.FARMER_WARP) {
            // Check if player has a pickaxe in hand
            if (!hasPickaxeInHand(player)) {
                return;
            }
        }

        // Find blocks to break based on location
        List<Block> blocksToBreak = findBlocksToBreak(player, locationType);
        if (blocksToBreak.isEmpty()) {
            return;
        }

        // Determine how many blocks to break
        int blockCount = getRandomBlockCount(locationType);
        blockCount = Math.min(blockCount, blocksToBreak.size());

        // Break the blocks
        for (int i = 0; i < blockCount; i++) {
            Block block = blocksToBreak.get(i);
            if (locationType == LocationType.FARMER_WARP) {
                // Call the PlayerInteractEvent to simulate a player breaking the block
                // This will trigger the CropsManager to handle the block break
                PlayerInteractEvent event = new PlayerInteractEvent(player, Action.LEFT_CLICK_BLOCK, new ItemStack(player.getInventory().getItemInMainHand()), block, BlockFace.DOWN);
                Bukkit.getPluginManager().callEvent(event);
            } else {
                breakBlock(player, block);
            }
        }
    }

    /**
     * Checks if a player is eligible for Mine Buddy processing
     */
    private boolean isPlayerEligible(Player player) {
        // Check if player is online
        if (!player.isOnline()) {
            return false;
        }

        return true;
    }

    /**
     * Checks if player has a pickaxe in their main hand
     */
    public boolean hasPickaxeInHand(Player player) {
        ItemStack itemInHand = player.getInventory().getItemInMainHand();
        if (itemInHand == null || itemInHand.getType() == Material.AIR) {
            return false;
        }

        // Check for any type of pickaxe
        Material type = itemInHand.getType();
        return type == Material.WOOD_PICKAXE ||
               type == Material.STONE_PICKAXE ||
               type == Material.IRON_PICKAXE ||
               type == Material.GOLD_PICKAXE ||
               type == Material.DIAMOND_PICKAXE;
    }

    /**
     * Determines the location type of a player
     */
    public LocationType getPlayerLocationType(Player player) {
        Location playerLoc = player.getLocation();

        // Check if in dungeon regions first
        if (isInDungeonRegion(player)) {
            return LocationType.DUNGEON;
        }

        // Check if in mine
        if (isInMineWorld(player)) {
            return LocationType.MINE;
        }

        // Check if in farmer warp area
        if (isInFarmerWarp(player)) {
            return LocationType.FARMER_WARP;
        }

        return LocationType.NONE;
    }

    /**
     * Checks if player is in a dungeon region (dungeon1 or dungeon2)
     */
    private boolean isInDungeonRegion(Player player) {
        try {
            Vector v = new Vector(player.getLocation().getX(), player.getLocation().getBlockY(), player.getLocation().getZ());
            List<String> regionNames = plugin.getWorldGuard().getRegionManager(player.getWorld()).getApplicableRegionsIDs(v);
            String regionsString = regionNames.toString().replace("[", "").replace("]", "");

            return regionsString.contains("dungeon1") || regionsString.contains("dungeon2");
        } catch (Exception e) {
            plugin.getLogger().warning("Error checking dungeon region for player " + player.getName() + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Checks if player is in their mine region
     */
    private boolean isInMineWorld(Player player) {
        try {
            // Check if player world is "PrestigeMine"
            if (!player.getWorld().getName().equalsIgnoreCase("PrestigeMine")) {
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error checking mine region for player " + player.getName() + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Checks if player is in farmer warp area
     * This is a simplified check - you may need to adjust based on your server's setup
     */
    private boolean isInFarmerWarp(Player player) {
        try {
            Vector v = new Vector(player.getLocation().getX(), player.getLocation().getBlockY(), player.getLocation().getZ());
            List<String> regionNames = plugin.getWorldGuard().getRegionManager(player.getWorld()).getApplicableRegionsIDs(v);
            String regionsString = regionNames.toString().replace("[", "").replace("]", "");

            if (regionsString.contains("skyfarm")) {
                return hasWheatBlocksNearby(player, 10);
            }
            return false;
        } catch (Exception e) {
            plugin.getLogger().warning("Error checking dungeon region for player " + player.getName() + ": " + e.getMessage());
            return false;
        }
    }

    /**
     * Checks if there are wheat blocks within a certain radius of the player
     */
    private boolean hasWheatBlocksNearby(Player player, int radius) {
        Location center = player.getLocation();
        World world = center.getWorld();

        for (int x = -radius; x <= radius; x++) {
            for (int y = -2; y <= 2; y++) {
                for (int z = -radius; z <= radius; z++) {
                    Block block = world.getBlockAt(center.getBlockX() + x, center.getBlockY() + y, center.getBlockZ() + z);
                    if (block.getType() == Material.CROPS) {
                        // Check if wheat is fully grown (data value 7)
                        if (block.getData() == 7) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * Finds blocks to break based on player location type
     */
    private List<Block> findBlocksToBreak(Player player, LocationType locationType) {
        switch (locationType) {
            case MINE:
                return findMineBlocks(player);
            case FARMER_WARP:
                return findWheatBlocks(player);
            case DUNGEON:
                return findDungeonOres(player);
            default:
                return new ArrayList<>();
        }
    }

    /**
     * Gets random block count based on location type
     */
    private int getRandomBlockCount(LocationType locationType) {
        switch (locationType) {
            case MINE:
                return ThreadLocalRandom.current().nextInt(1, 6); // 1-5 blocks
            case FARMER_WARP:
                return ThreadLocalRandom.current().nextInt(1, 3); // 1-2 blocks
            case DUNGEON:
                return ThreadLocalRandom.current().nextInt(1, 4); // 1-3 blocks
            default:
                return 0;
        }
    }

    // Block finding methods will be implemented in the next phase
    private List<Block> findMineBlocks(Player player) {
        List<Block> blocks = new ArrayList<>();

        try {
            // Get the mine manager from the main plugin
            Main mineSystem = plugin.getMineSystem();
            if (mineSystem == null) {
                return blocks;
            }

            // Get the mine manager from the main plugin
            MineManager mineManager = mineSystem.getMineList().get(player.getUniqueId());
            if (mineManager == null) {
                return blocks;
            }

            // Get blocks in a small radius around the player within their mine
            Location mineCenter = mineSystem.getCenterOfMine(mineManager.getOwner());
            World world = mineCenter.getWorld();
            int radius = 5; // Search within 5 blocks radius

            for (int x = -radius; x <= radius; x++) {
                for (int y = -2; y <= 2; y++) { // Check 2 blocks up and down
                    for (int z = -radius; z <= radius; z++) {
                        Location blockLoc = mineCenter.clone().add(x, y, z);
                        Block block = world.getBlockAt(blockLoc);
                        blocks.add(block);
                    }
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("Error finding mine blocks for " + player.getName() + ": " + e.getMessage());
        }

        return blocks;
    }

    private List<Block> findWheatBlocks(Player player) {
        List<Block> blocks = new ArrayList<>();

        try {
            Location playerLoc = player.getLocation();
            World world = playerLoc.getWorld();
            int radius = 10; // Search within 10 blocks radius for wheat

            for (int x = -radius; x <= radius; x++) {
                for (int y = -1; y <= 1; y++) { // Check 1 block up and down
                    for (int z = -radius; z <= radius; z++) {
                        Location blockLoc = playerLoc.clone().add(x, y, z);
                        Block block = world.getBlockAt(blockLoc);

                        // Check if block is fully grown wheat
                        if (block.getType() == Material.CROPS) {
                            // Check if wheat is fully grown (data value 7)
                            if (block.getData() == 7) {
                                blocks.add(block);
                            }
                            // plugin.getCropsManager().isFullyGrown(block);
                        }
                    }
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("Error finding wheat blocks for " + player.getName() + ": " + e.getMessage());
        }

        return blocks;
    }

    private List<Block> findDungeonOres(Player player) {
        List<Block> blocks = new ArrayList<>();

        try {
            Location playerLoc = player.getLocation();
            World world = playerLoc.getWorld();
            int radius = 6; // Search within 6 blocks radius for ores

            // Define dungeon ore types
            List<Material> dungeonOres = Arrays.asList(
                Material.IRON_ORE,
                Material.DIAMOND_ORE,
                Material.COAL_ORE,
                Material.EMERALD_ORE
            );

            for (int x = -radius; x <= radius; x++) {
                for (int y = -3; y <= 3; y++) { // Check 3 blocks up and down
                    for (int z = -radius; z <= radius; z++) {
                        Location blockLoc = playerLoc.clone().add(x, y, z);
                        Block block = world.getBlockAt(blockLoc);

                        // Check if block is a dungeon ore type
                        if (dungeonOres.contains(block.getType())) {
                            // debug
                            player.sendMessage("Found ore: " + block.getType().name());

                            // Verify player can break this block (has permission)
                            if (canPlayerBreakBlock(player, block)) {
                                // debug
                                player.sendMessage("Can break ore: " + block.getType().name());
                                blocks.add(block);
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("Error finding dungeon ores for " + player.getName() + ": " + e.getMessage());
        }

        return blocks;
    }

    /**
     * Breaks a block by creating a synthetic BlockBreakEvent
     */
    private void breakBlock(Player player, Block block) {
        try {
            // Create synthetic BlockBreakEvent following the pattern from BlockBreakTask
            BlockBreakEvent syntheticEvent = new BlockBreakEvent(block, player);
            // Call the event to allow other plugins to handle it
            Bukkit.getPluginManager().callEvent(syntheticEvent);

            /*// If event is not cancelled, process through the main block break system
            if (!syntheticEvent.isCancelled()) {
                // Call the main plugin's blockbreakOriginal method
                // This ensures all the existing logic (rewards, enchants, etc.) is applied
                BlockBreakData blockBreakData = new BlockBreakData(block.getType(), block.getData(), block.getWorld().getName(), block.getX(), block.getY(), block.getZ(), block);
                plugin.blockbreakOriginal(syntheticEvent, blockBreakData);
            }*/

        } catch (Exception e) {
            plugin.getLogger().warning("Error breaking block for " + player.getName() + " at " +
                block.getLocation() + ": " + e.getMessage());
        }
    }

    /**
     * Checks if player can break a specific block (permission check)
     */
    private boolean canPlayerBreakBlock(Player player, Block block) {
        try {
            // Get applicable regions for the block
            ApplicableRegionSet set = plugin.getWorldGuard().getRegionManager(player.getWorld()).getApplicableRegions(block.getLocation());
            // Check if player has permission to break the specific block
            if (set.queryState(null, DefaultFlag.BLOCK_BREAK) == StateFlag.State.DENY) {
                return false;
            }
        } catch (Exception e) {
            // If there's an error, assume the player can't break it
            return false;
        }
    }
}

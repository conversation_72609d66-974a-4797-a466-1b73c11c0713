package it.masterzen.minebuddy;

import it.masterzen.blockbreak.AlphaBlockBreak;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Command handler for Mine Buddy system
 * Provides commands for testing and managing Mine Buddy functionality
 */
public class MineB<PERSON>yCommand implements CommandExecutor {
    
    private final AlphaBlockBreak plugin;
    private final MineBuddyManager manager;
    
    public MineBuddyCommand(AlphaBlockBreak plugin, MineBuddyManager manager) {
        this.plugin = plugin;
        this.manager = manager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cThis command can only be used by players!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "status":
                showStatus(player);
                break;
            case "test":
                testMineBuddy(player);
                break;
            case "reload":
                if (player.hasPermission("minebuddy.admin")) {
                    manager.reload();
                    player.sendMessage("§a§lMINE BUDDY §8»§7 Configuration reloaded!");
                } else {
                    player.sendMessage("§c§lMINE BUDDY §8»§7 You don't have permission to use this command!");
                }
                break;
            case "toggle":
                if (player.hasPermission("minebuddy.admin")) {
                    toggleMineBuddy(player);
                } else {
                    player.sendMessage("§c§lMINE BUDDY §8»§7 You don't have permission to use this command!");
                }
                break;
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void sendHelp(Player player) {
        player.sendMessage("§e§lMINE BUDDY §8»§7 Commands:");
        player.sendMessage("§7  /minebuddy status - Show current status");
        player.sendMessage("§7  /minebuddy test - Test Mine Buddy on your location");
        if (player.hasPermission("minebuddy.admin")) {
            player.sendMessage("§7  /minebuddy reload - Reload configuration");
            player.sendMessage("§7  /minebuddy toggle - Toggle Mine Buddy on/off");
        }
    }
    
    private void showStatus(Player player) {
        MineBuddy mineBuddy = manager.getMineBuddy();
        MineBuddy.LocationType location = mineBuddy.getPlayerLocationType(player);
        
        player.sendMessage("§e§lMINE BUDDY §8»§7 Status:");
        player.sendMessage("§7  Enabled: " + (manager.isEnabled() ? "§aYes" : "§cNo"));
        player.sendMessage("§7  Your location: §e" + location.name());
        player.sendMessage("§7  Has pickaxe: " + (mineBuddy.hasPickaxeInHand(player) ? "§aYes" : "§cNo"));
        player.sendMessage("§7  Task interval: §e" + manager.getTaskInterval() + " ticks");
    }
    
    private void testMineBuddy(Player player) {
        MineBuddy mineBuddy = manager.getMineBuddy();
        
        player.sendMessage("§e§lMINE BUDDY §8»§7 Testing...");
        
        // Test location detection
        MineBuddy.LocationType location = mineBuddy.getPlayerLocationType(player);
        player.sendMessage("§7  Location type: §e" + location.name());
        
        // Test pickaxe detection
        boolean hasPickaxe = mineBuddy.hasPickaxeInHand(player);
        player.sendMessage("§7  Has pickaxe: " + (hasPickaxe ? "§aYes" : "§cNo"));
        
        // Test eligibility
        if (location != MineBuddy.LocationType.NONE && hasPickaxe) {
            player.sendMessage("§7  Eligibility: §aEligible for Mine Buddy processing");
            
            // Manually trigger one processing cycle for this player
            mineBuddy.processPlayer(player);
            player.sendMessage("§7  Test processing triggered!");
        } else {
            player.sendMessage("§7  Eligibility: §cNot eligible");
            if (location == MineBuddy.LocationType.NONE) {
                player.sendMessage("§7    Reason: Not in a valid location");
            }
            if (!hasPickaxe) {
                player.sendMessage("§7    Reason: No pickaxe in hand");
            }
        }
    }
    
    private void toggleMineBuddy(Player player) {
        // This would require adding a toggle method to MineBuddyManager
        player.sendMessage("§e§lMINE BUDDY §8»§7 Toggle functionality not yet implemented");
    }
}

# Mine Buddy System

## Panoramica
Il sistema Mine Buddy è una nuova funzionalità per AlphaBlockBreak che simula automaticamente la rottura dei blocchi per i giocatori online in base alla loro posizione. Il sistema funziona ogni 5 secondi e controlla tutti i giocatori online per determinare se sono idonei per il processing automatico.

## Funzionalità Principali

### Rilevamento della Posizione
Il sistema rileva automaticamente dove si trova il giocatore:
- **MINE**: Nelle miniere private (integrazione con PrestigeMine)
- **FARMER_WARP**: Nelle aree di farming (rileva grano maturo)
- **DUNGEON**: Nelle regioni dungeon1 o dungeon2 (WorldGuard)
- **NONE**: Nessuna posizione valida

### Validazione del Giocatore
Prima di processare un giocatore, il sistema verifica:
- Il giocatore è online
- Il giocatore si trova in una posizione valida
- Il giocatore ha un piccone in mano (richiesto solo per MINE e DUNGEON, non per FARMER_WARP)

### Rottura Automatica dei Blocchi
In base alla posizione, il sistema rompe diversi tipi di blocchi:

#### Miniere (MINE)
- Rompe da 1 a 5 blocchi casuali
- Cerca blocchi rompibili nel raggio di 5 blocchi dal giocatore
- Verifica che i blocchi siano all'interno dei confini della miniera del giocatore
- **Richiede piccone in mano**

#### Farmer Warp (FARMER_WARP)
- Rompe da 1 a 2 blocchi di grano maturo
- Cerca grano completamente cresciuto (data value 7) nel raggio di 8 blocchi
- **Non richiede piccone in mano** (logico per il farming)

#### Dungeon (DUNGEON)
- Rompe da 1 a 3 minerali casuali
- Cerca IRON_ORE, DIAMOND_ORE, COAL_ORE, EMERALD_ORE nel raggio di 6 blocchi
- Verifica i permessi di rottura per ogni blocco
- **Richiede piccone in mano**

## Struttura del Codice

### Classi Principali

#### `MineBuddyManager`
- Gestisce la configurazione del sistema
- Inizializza e controlla il MineBuddy
- Gestisce il file di configurazione `minebuddy.yml`
- Registra i comandi

#### `MineBuddy`
- Contiene la logica principale del sistema
- Gestisce il task schedulato (BukkitRunnable)
- Implementa la rilevazione della posizione
- Gestisce la rottura dei blocchi tramite eventi sintetici

#### `MineBuddyCommand`
- Fornisce comandi per testare e gestire il sistema
- Comandi disponibili: status, test, reload, toggle

### Integrazione con AlphaBlockBreak
Il sistema è completamente integrato nel plugin principale:
- Inizializzazione in `onEnable()`
- Shutdown pulito in `onDisable()`
- Utilizza il sistema `blockbreakOriginal()` esistente
- Mantiene tutte le funzionalità esistenti (enchant, rewards, etc.)

## Configurazione

Il file `minebuddy.yml` viene creato automaticamente con le seguenti opzioni:

```yaml
enabled: true
task-interval-ticks: 100  # 5 secondi
mine:
  blocks-min: 1
  blocks-max: 5
farmer:
  blocks-min: 1
  blocks-max: 2
dungeon:
  blocks-min: 1
  blocks-max: 3
```

## Comandi

### `/minebuddy` (alias: `/mb`, `/buddy`)
- `status` - Mostra lo stato attuale del sistema
- `test` - Testa il Mine Buddy sulla tua posizione
- `reload` - Ricarica la configurazione (richiede permesso `minebuddy.admin`)
- `toggle` - Attiva/disattiva il sistema (richiede permesso `minebuddy.admin`)

## Sicurezza e Performance

### Eventi Sintetici
Il sistema crea `BlockBreakEvent` sintetici che:
- Passano attraverso tutti i controlli di sicurezza esistenti
- Rispettano i permessi dei giocatori
- Integrano con tutti i plugin di protezione
- Mantengono la compatibilità con il sistema esistente

### Gestione degli Errori
- Logging completo degli errori
- Gestione sicura delle eccezioni
- Fallback graceful in caso di problemi

### Performance
- Task asincrono per evitare lag del server
- Controlli ottimizzati per ridurre il carico
- Configurazione flessibile dell'intervallo di esecuzione

## Installazione e Attivazione

1. Il sistema è automaticamente attivo quando il plugin viene caricato
2. La configurazione viene creata automaticamente
3. I comandi sono registrati automaticamente
4. Non richiede configurazione aggiuntiva per funzionare

## Note per lo Sviluppo Futuro

### GUI di Miglioramento
Come richiesto dall'utente, è possibile implementare una GUI per permettere ai giocatori di:
- Migliorare l'efficacia del Mine Buddy
- Aumentare il numero di blocchi rotti
- Ridurre l'intervallo di tempo
- Aggiungere nuovi tipi di blocchi

### Estensioni Possibili
- Supporto per più tipi di strumenti (asce per legno, pale per terra)
- Integrazione con sistemi di livelli/esperienza
- Statistiche personalizzate per giocatore
- Effetti visivi e sonori
- Integrazione con sistemi di economia avanzati

## Compatibilità

Il sistema è compatibile con:
- Bukkit/Spigot 1.12.2
- WorldGuard (per rilevamento regioni)
- PrestigeMine (per integrazione miniere)
- Tutti i plugin di protezione esistenti
- Sistema di enchant TokenEnchant esistente
